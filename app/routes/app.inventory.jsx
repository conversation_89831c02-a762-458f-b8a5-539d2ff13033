import { useState, useCallback, useMemo } from "react";

import { useLoaderData, useNavigation } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  DataTable,
  Text,
  Badge,
  Spinner,
  EmptyState,
  Filters,
  ChoiceList,
  Tabs,
  Box,
  InlineStack,
  BlockStack
} from "@shopify/polaris";

import { InventoryCalculationService } from "../services/inventory/InventoryCalculationService.js";

export const loader = async ({ request }) => {
  // Import server-only functions inside the loader
  const { authenticateMultistore } = await import("../shopify.multistore.server");

  // Use multistore authentication
  const result = await authenticateMultistore(request);

  // If result is a Response (redirect), throw it to let Remix handle it
  if (result instanceof Response) {
    throw result;
  }

  const { session } = result;

  // Import server-only functions to check if this is an admin shop
  const { isAdminShop } = await import("../lib/config/index.js");
  const isAdmin = isAdminShop(session.shop);

  // Only allow admin shops to access this page
  if (!isAdmin) {
    return new Response("Access denied. Admin privileges required.", { status: 403 });
  }

  try {

    const inventoryService = new InventoryCalculationService();

    // Get aggregated inventory requirements for all stores
    const inventoryData = await inventoryService.getAllInventoryRequirements({
      minQuantity: 0, // Include all items, even with 0 quantity
      includeTransactions: false // Don't include transaction history for performance
    });

    console.log('Inventory data loaded:', {
      regular: inventoryData.regular?.length || 0,
      engraving: inventoryData.engraving?.length || 0,
      totalRegular: inventoryData.regular?.reduce((sum, item) => sum + item.requiredQuantity, 0) || 0
    });

    return Response.json({
      inventoryData,
      shop: session.shop,
      isAdmin,
      success: true
    });

  } catch (error) {
    console.error("Error loading inventory requirements:", error);

    return Response.json({
      inventoryData: { regular: [], engraving: [] },
      shop: session.shop,
      success: false,
      error: error.message || error.toString() || "Unknown error occurred"
    });
  }
};

export default function InventoryPage() {
  const { inventoryData, shop, isAdmin, success, error } = useLoaderData();
  const navigation = useNavigation();
  const isLoading = navigation.state === "loading";

  // Provide fallback for inventoryData
  const safeInventoryData = inventoryData || { regular: [], engraving: [] };

  // State for filtering and search
  const [selectedTab, setSelectedTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilters, setSelectedFilters] = useState({
    garmentCode: [],
    color: [],
    size: []
  });

  // Tab configuration
  const tabs = [
    {
      id: "regular",
      content: "Regular Items",
      accessibilityLabel: "Regular inventory items",
      panelID: "regular-items-panel"
    },
    {
      id: "engraving",
      content: "Engraving Items",
      accessibilityLabel: "Engraving inventory items",
      panelID: "engraving-items-panel"
    }
  ];

  // Get current data based on selected tab
  const currentData = selectedTab === 0 ? safeInventoryData.regular : safeInventoryData.engraving;
  const currentCategory = selectedTab === 0 ? "regular" : "engraving";

  // Extract unique filter options from current data
  const filterOptions = useMemo(() => {
    // Define the standard size order
    const sizeOrder = ['XS', 'S', 'M', 'L', 'XL', '2XL', '3XL'];

    const garmentCodes = [...new Set(currentData.map(item => item.garmentCode))].sort();
    const colors = [...new Set(currentData.map(item => item.color))].sort();
    const availableSizes = [...new Set(currentData.map(item => item.size))];

    // Sort sizes according to the standard order, keeping any non-standard sizes at the end
    const sizes = sizeOrder.filter(size => availableSizes.includes(size))
      .concat(availableSizes.filter(size => !sizeOrder.includes(size)).sort());

    return {
      garmentCode: garmentCodes.map(code => ({ label: code, value: code })),
      color: colors.map(color => ({ label: color, value: color })),
      size: sizes.map(size => ({ label: size, value: size }))
    };
  }, [currentData]);

  // Filter and search data
  const filteredData = useMemo(() => {
    let filtered = currentData;

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.garmentCode.toLowerCase().includes(query) ||
        item.color.toLowerCase().includes(query) ||
        item.size.toLowerCase().includes(query)
      );
    }

    // Apply selected filters
    if (selectedFilters.garmentCode.length > 0) {
      filtered = filtered.filter(item => selectedFilters.garmentCode.includes(item.garmentCode));
    }
    if (selectedFilters.color.length > 0) {
      filtered = filtered.filter(item => selectedFilters.color.includes(item.color));
    }
    if (selectedFilters.size.length > 0) {
      filtered = filtered.filter(item => selectedFilters.size.includes(item.size));
    }

    return filtered;
  }, [currentData, searchQuery, selectedFilters]);

  // Group filtered data by garment code
  const groupedData = useMemo(() => {
    const groups = {};

    filteredData.forEach(item => {
      if (!groups[item.garmentCode]) {
        groups[item.garmentCode] = [];
      }
      groups[item.garmentCode].push(item);
    });

    // Sort garment codes
    const sortedGroups = {};
    Object.keys(groups).sort().forEach(garmentCode => {
      sortedGroups[garmentCode] = groups[garmentCode];
    });

    return sortedGroups;
  }, [filteredData]);

  // Calculate totals
  const totals = useMemo(() => {
    const totalItems = filteredData.length;
    const totalQuantity = filteredData.reduce((sum, item) => sum + item.requiredQuantity, 0);
    const itemsWithRequirements = filteredData.filter(item => item.requiredQuantity > 0).length;
    const totalGarmentCodes = Object.keys(groupedData).length;

    return { totalItems, totalQuantity, itemsWithRequirements, totalGarmentCodes };
  }, [filteredData, groupedData]);

  // Helper function to create a table for a specific garment code
  const createGarmentTable = useCallback((garmentCode, items) => {
    // Define the standard size order
    const sizeOrder = ['XS', 'S', 'M', 'L', 'XL', '2XL', '3XL'];

    // Get unique colors and sizes for this garment
    const colors = [...new Set(items.map(item => item.color))].sort();
    const availableSizes = [...new Set(items.map(item => item.size))];

    // Sort sizes according to the standard order, keeping any non-standard sizes at the end
    const sizes = sizeOrder.filter(size => availableSizes.includes(size))
      .concat(availableSizes.filter(size => !sizeOrder.includes(size)).sort());

    // Create a matrix of color x size with quantities
    const matrix = {};
    items.forEach(item => {
      if (!matrix[item.color]) {
        matrix[item.color] = {};
      }
      matrix[item.color][item.size] = item.requiredQuantity;
    });

    // Create table headings: Color column + size columns
    const headings = ["Color", ...sizes];

    // Create table rows
    const rows = colors.map(color => {
      const row = [color];
      sizes.forEach(size => {
        const quantity = matrix[color]?.[size] || 0;
        row.push(
          <Badge
            key={`${color}-${size}`}
            tone={quantity > 0 ? "attention" : "success"}
          >
            {quantity}
          </Badge>
        );
      });
      return row;
    });

    // Calculate total for this garment
    const totalQuantity = items.reduce((sum, item) => sum + item.requiredQuantity, 0);
    const itemsWithRequirements = items.filter(item => item.requiredQuantity > 0).length;

    return {
      headings,
      rows,
      totalQuantity,
      itemsWithRequirements,
      totalItems: items.length
    };
  }, []);

  // Filter handlers
  const handleFiltersChange = useCallback((filters) => {
    setSelectedFilters(filters);
  }, []);

  const handleSearchChange = useCallback((value) => {
    setSearchQuery(value);
  }, []);

  const handleFiltersClearAll = useCallback(() => {
    setSelectedFilters({
      garmentCode: [],
      color: [],
      size: []
    });
    setSearchQuery("");
  }, []);

  // Filter components
  const filters = [
    {
      key: "garmentCode",
      label: "Garment Code",
      filter: (
        <ChoiceList
          title="Garment Code"
          titleHidden
          choices={filterOptions.garmentCode}
          selected={selectedFilters.garmentCode}
          onChange={(value) => handleFiltersChange({ ...selectedFilters, garmentCode: value })}
          allowMultiple
        />
      ),
      shortcut: true
    },
    {
      key: "color",
      label: "Color",
      filter: (
        <ChoiceList
          title="Color"
          titleHidden
          choices={filterOptions.color}
          selected={selectedFilters.color}
          onChange={(value) => handleFiltersChange({ ...selectedFilters, color: value })}
          allowMultiple
        />
      ),
      shortcut: true
    },
    {
      key: "size",
      label: "Size",
      filter: (
        <ChoiceList
          title="Size"
          titleHidden
          choices={filterOptions.size}
          selected={selectedFilters.size}
          onChange={(value) => handleFiltersChange({ ...selectedFilters, size: value })}
          allowMultiple
        />
      ),
      shortcut: true
    }
  ];

  const appliedFilters = [];
  if (selectedFilters.garmentCode.length > 0) {
    appliedFilters.push({
      key: "garmentCode",
      label: `Garment Code: ${selectedFilters.garmentCode.join(", ")}`,
      onRemove: () => handleFiltersChange({ ...selectedFilters, garmentCode: [] })
    });
  }
  if (selectedFilters.color.length > 0) {
    appliedFilters.push({
      key: "color",
      label: `Color: ${selectedFilters.color.join(", ")}`,
      onRemove: () => handleFiltersChange({ ...selectedFilters, color: [] })
    });
  }
  if (selectedFilters.size.length > 0) {
    appliedFilters.push({
      key: "size",
      label: `Size: ${selectedFilters.size.join(", ")}`,
      onRemove: () => handleFiltersChange({ ...selectedFilters, size: [] })
    });
  }

  if (!success) {
    return (
      <Page title="Inventory Requirements">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">Error Loading Inventory Data</Text>
                <Text>{error || "An unknown error occurred while loading inventory requirements."}</Text>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>
      </Page>
    );
  }

  return (
    <Page
      title="Inventory Requirements"
      subtitle="Aggregated inventory needs across all orders"
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Tabs tabs={tabs} selected={selectedTab} onSelect={setSelectedTab}>
              <Box padding="400">
                {isLoading ? (
                  <div style={{ textAlign: "center", padding: "2rem" }}>
                    <Spinner size="large" />
                    <Text>Loading inventory requirements...</Text>
                  </div>
                ) : (
                  <BlockStack gap="400">
                    {/* Summary Stats */}
                    <InlineStack gap="400" align="space-between">
                      <Text variant="headingMd" as="h3">
                        {currentCategory === "regular" ? "Regular Items" : "Engraving Items"}
                      </Text>
                      <InlineStack gap="200">
                        <Badge tone="info">{totals.totalGarmentCodes} garment codes</Badge>
                        <Badge tone="attention">{totals.itemsWithRequirements} need inventory</Badge>
                        <Badge tone="warning">{totals.totalQuantity} total quantity needed</Badge>
                      </InlineStack>
                    </InlineStack>

                    {/* Filters */}
                    <Filters
                      queryValue={searchQuery}
                      queryPlaceholder="Search garment codes, colors, or sizes..."
                      filters={filters}
                      appliedFilters={appliedFilters}
                      onQueryChange={handleSearchChange}
                      onQueryClear={() => setSearchQuery("")}
                      onClearAll={handleFiltersClearAll}
                    />

                    {/* Grouped Tables by Garment Code */}
                    {Object.keys(groupedData).length === 0 ? (
                      <EmptyState
                        heading={searchQuery || appliedFilters.length > 0 ? "No items match your filters" : "No inventory requirements found"}
                        image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                      >
                        {searchQuery || appliedFilters.length > 0 ? (
                          <p>Try adjusting your search or filters to find what you're looking for.</p>
                        ) : (
                          <p>Inventory requirements will appear here once orders are processed.</p>
                        )}
                      </EmptyState>
                    ) : (
                      <BlockStack gap="600">
                        {Object.entries(groupedData).map(([garmentCode, items]) => {
                          const tableData = createGarmentTable(garmentCode, items);

                          return (
                            <Card key={garmentCode}>
                              <BlockStack gap="400">
                                <InlineStack gap="400" align="space-between">
                                  <Text variant="headingMd" as="h4">
                                    {garmentCode}
                                  </Text>
                                  <InlineStack gap="200">
                                    <Badge tone="info">{tableData.totalItems} items</Badge>
                                    <Badge tone="attention">{tableData.itemsWithRequirements} need inventory</Badge>
                                    <Badge tone="warning">{tableData.totalQuantity} total needed</Badge>
                                  </InlineStack>
                                </InlineStack>

                                <DataTable
                                  columnContentTypes={["text", ...Array(tableData.headings.length - 1).fill("text")]}
                                  headings={tableData.headings}
                                  rows={tableData.rows}
                                />
                              </BlockStack>
                            </Card>
                          );
                        })}
                      </BlockStack>
                    )}
                  </BlockStack>
                )}
              </Box>
            </Tabs>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
